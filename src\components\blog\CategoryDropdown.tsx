'use client';

import React, { useState, useEffect, forwardRef } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, Plus, X, Tag, Loader2 } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Category {
  _id: string;
  name: string;
  slug: string;
  count?: number;
}

interface CategoryDropdownProps {
  selectedCategoryIds: string[];
  onCategoriesChange: (categoryIds: string[]) => void;
  placeholder?: string;
  className?: string;
}

// Fix React 19 ref warning by properly forwarding refs
const CategoryDropdownContent = forwardRef<
  React.ElementRef<typeof DropdownMenuContent>,
  React.ComponentPropsWithoutRef<typeof DropdownMenuContent>
>(({ className, ...props }, ref) => (
  <DropdownMenuContent ref={ref} className={className} {...props} />
));
CategoryDropdownContent.displayName = "CategoryDropdownContent";

export function CategoryDropdown({
  selectedCategoryIds = [],
  onCategoriesChange,
  placeholder = "Select categories",
  className = "",
}: CategoryDropdownProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get selected categories for display
  const selectedCategories = categories.filter(cat => 
    selectedCategoryIds.includes(cat._id)
  );

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch('/api/categories');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (Array.isArray(data)) {
        setCategories(data);
      } else if (data.error) {
        throw new Error(data.error);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch categories');
      toast({
        title: 'Error',
        description: 'Failed to fetch categories',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const createCategory = async () => {
    if (!newCategoryName.trim()) {
      toast({
        title: 'Error',
        description: 'Category name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    // Check if category already exists (case-insensitive)
    const existingCategory = categories.find(
      cat => cat.name.toLowerCase() === newCategoryName.trim().toLowerCase()
    );
    
    if (existingCategory) {
      toast({
        title: 'Error',
        description: 'Category already exists',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsCreating(true);
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newCategoryName.trim() }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      const newCategory = await response.json();
      
      // Update categories list
      setCategories(prev => [...prev, newCategory]);

      // Auto-select the newly created category
      const updatedCategoryIds = [...selectedCategoryIds, newCategory._id];
      onCategoriesChange(updatedCategoryIds);

      setNewCategoryName('');
      toast({
        title: 'Success',
        description: `Category "${newCategory.name}" created and selected`,
      });
    } catch (error) {
      console.error('Error creating category:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create category',
        variant: 'destructive',
      });
    } finally {
      setIsCreating(false);
    }
  };

  const toggleCategory = (categoryId: string) => {
    const isSelected = selectedCategoryIds.includes(categoryId);
    const updatedIds = isSelected
      ? selectedCategoryIds.filter(id => id !== categoryId)
      : [...selectedCategoryIds, categoryId];

    onCategoriesChange(updatedIds);
  };

  const removeCategory = (categoryId: string) => {
    const updatedIds = selectedCategoryIds.filter(id => id !== categoryId);
    onCategoriesChange(updatedIds);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      createCategory();
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return (
    <div className={`space-y-3 ${className}`} role="group" aria-label="Category selection">
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            className="w-full justify-between h-10 px-3 py-2" 
            type="button"
            aria-expanded={isOpen}
            aria-haspopup="menu"
            aria-label={selectedCategories.length > 0 
              ? `${selectedCategories.length} categories selected` 
              : placeholder
            }
          >
            <div className="flex items-center gap-2 min-w-0">
              <Tag className="h-4 w-4 flex-shrink-0" />
              <span className="truncate">
                {selectedCategories.length > 0
                  ? `${selectedCategories.length} categor${selectedCategories.length === 1 ? 'y' : 'ies'} selected`
                  : placeholder}
              </span>
            </div>
            <ChevronDown className="h-4 w-4 opacity-50 flex-shrink-0" />
          </Button>
        </DropdownMenuTrigger>

        <CategoryDropdownContent className="w-80 max-h-80 overflow-y-auto" align="start">
          <DropdownMenuLabel className="font-semibold">Select Categories</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {loading ? (
            <div className="p-4 text-center">
              <Loader2 className="h-4 w-4 animate-spin mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Loading categories...</p>
            </div>
          ) : error ? (
            <div className="p-4 text-center">
              <p className="text-sm text-destructive mb-2">Error: {error}</p>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={fetchCategories}
                className="text-xs"
              >
                Retry
              </Button>
            </div>
          ) : categories.length > 0 ? (
            <div className="max-h-48 overflow-y-auto">
              {categories.map((category) => {
                const isSelected = selectedCategoryIds.includes(category._id);
                return (
                  <DropdownMenuItem
                    key={category._id}
                    onClick={() => toggleCategory(category._id)}
                    className="flex items-center justify-between cursor-pointer px-3 py-2 hover:bg-accent focus:bg-accent"
                    role="menuitemcheckbox"
                    aria-checked={isSelected}
                  >
                    <span className="flex-1 truncate">{category.name}</span>
                    {isSelected && (
                      <div 
                        className="h-2 w-2 rounded-full bg-primary flex-shrink-0 ml-2" 
                        aria-label="Selected"
                      />
                    )}
                  </DropdownMenuItem>
                );
              })}
            </div>
          ) : (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No categories found
            </div>
          )}

          <DropdownMenuSeparator />

          <div className="p-3 space-y-2">
            <div className="flex gap-2">
              <Input
                placeholder="New category name"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                onKeyDown={handleKeyDown}
                className="flex-1 h-8 text-sm"
                disabled={isCreating}
                aria-label="New category name"
              />
              <Button
                size="sm"
                onClick={createCategory}
                disabled={isCreating || !newCategoryName.trim()}
                type="button"
                className="h-8 px-3"
                aria-label="Create new category"
              >
                {isCreating ? (
                  <Loader2 className="h-3 w-3 animate-spin" />
                ) : (
                  <Plus className="h-3 w-3" />
                )}
              </Button>
            </div>
          </div>
        </CategoryDropdownContent>
      </DropdownMenu>

      {/* Selected Categories Display */}
      {selectedCategories.length > 0 && (
        <div className="flex flex-wrap gap-2" role="group" aria-label="Selected categories">
          {selectedCategories.map((category) => (
            <Badge 
              key={category._id} 
              variant="secondary" 
              className="flex items-center gap-1 px-2 py-1 text-xs"
            >
              <span className="truncate max-w-32">{category.name}</span>
              <button
                onClick={() => removeCategory(category._id)}
                className="ml-1 hover:text-destructive focus:text-destructive focus:outline-none"
                aria-label={`Remove ${category.name} category`}
                type="button"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
