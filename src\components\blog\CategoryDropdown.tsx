'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, Plus, X, Tag } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Category {
  _id: string;
  name: string;
  slug: string;
  count?: number;
}

interface CategoryDropdownProps {
  selectedCategories: Category[];
  onCategoriesChange: (categories: Category[]) => void;
  placeholder?: string;
  className?: string;
}

export function CategoryDropdown({
  selectedCategories = [],
  onCategoriesChange,
  placeholder = "Select categories",
  className = "",
}: CategoryDropdownProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/categories');
      const data = await response.json();

      if (data.success) {
        setCategories(data.data || []);
      } else {
        console.error('Failed to fetch categories:', data.error);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const createCategory = async () => {
    if (!newCategoryName.trim()) {
      toast({
        title: 'Error',
        description: 'Category name cannot be empty',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsCreating(true);
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newCategoryName.trim() }),
      });

      const data = await response.json();

      if (data.success) {
        const newCategory = data.data;
        setCategories(prev => [...prev, newCategory]);

        const updatedCategories = [...selectedCategories, newCategory];
        onCategoriesChange(updatedCategories);

        setNewCategoryName('');
        toast({
          title: 'Success',
          description: `Category "${newCategory.name}" created and selected`,
        });
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to create category',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error creating category:', error);
      toast({
        title: 'Error',
        description: 'Failed to create category',
        variant: 'destructive',
      });
    } finally {
      setIsCreating(false);
    }
  };

  const toggleCategory = (category: Category) => {
    const exists = selectedCategories.some(c => c._id === category._id);
    const updated = exists
      ? selectedCategories.filter(c => c._id !== category._id)
      : [...selectedCategories, category];

    onCategoriesChange(updated);
  };

  const removeCategory = (id: string) => {
    const updated = selectedCategories.filter(c => c._id !== id);
    onCategoriesChange(updated);
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  return (
    <div className={`space-y-2 ${className}`}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="w-full justify-between" type="button">
            <div className="flex items-center gap-2">
              <Tag className="h-4 w-4" />
              {selectedCategories.length > 0
                ? `${selectedCategories.length} categor${selectedCategories.length === 1 ? 'y' : 'ies'} selected`
                : placeholder}
            </div>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent className="w-80" align="start">
          <DropdownMenuLabel>Select Categories</DropdownMenuLabel>
          <DropdownMenuSeparator />

          {loading ? (
            <div className="p-2 text-center text-sm text-muted-foreground">
              Loading categories...
            </div>
          ) : categories.length > 0 ? (
            categories.map((category) => {
              const isSelected = selectedCategories.some(c => c._id === category._id);
              return (
                <DropdownMenuItem
                  key={category._id}
                  onClick={() => toggleCategory(category)}
                  className="flex items-center justify-between cursor-pointer"
                >
                  <span>{category.name}</span>
                  {isSelected && <div className="h-2 w-2 rounded-full bg-primary" />}
                </DropdownMenuItem>
              );
            })
          ) : (
            <div className="p-2 text-center text-sm text-muted-foreground">
              No categories found
            </div>
          )}

          <DropdownMenuSeparator />

          <div className="p-2 space-y-2">
            <div className="flex gap-2">
              <Input
                placeholder="New category name"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    createCategory();
                  }
                }}
                className="flex-1"
              />
              <Button
                size="sm"
                onClick={createCategory}
                disabled={isCreating || !newCategoryName.trim()}
                type="button"
              >
                {isCreating ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                ) : (
                  <Plus className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {selectedCategories.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedCategories.map((category) => (
            <Badge key={category._id} variant="secondary" className="flex items-center gap-1">
              {category.name}
              <X
                className="h-3 w-3 cursor-pointer hover:text-destructive"
                onClick={() => removeCategory(category._id)}
              />
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
