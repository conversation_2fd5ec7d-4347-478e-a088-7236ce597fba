// This is a server component
import { Metadata } from "next";
import { notFound } from "next/navigation";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { BlogPostContent } from "@/components/blog/BlogPostContent";
import connectToDatabase from "@/lib/db";
import BlogPost from "@/models/BlogPost";
import "@/models/User"; // Import User model for population

import { blogPosts } from "@/data/blog-posts";

interface Props {
  params: Promise<{
    slug: string;
  }>;
}



interface Post {
  _id?: string;
  title: string;
  date: string;
  author: string;
  category: string;
  image: string;
  content: string;
  description?: string;
  imageCredit?: string;
  tags?: string[];
}

async function getBlogPost(slug: string): Promise<Post | null> {
  try {
    await connectToDatabase();
    const post = await BlogPost.findOne({ slug }).populate("authorId", "name");

    if (!post) {
      // Fallback to static data
      const staticPost = blogPosts[slug as keyof typeof blogPosts];
      return staticPost || null;
    }

    // Format the post data to match BlogPostContent interface
    return {
      _id: post._id.toString(),
      title: post.title,
      content: post.content,
      date: post.publishedAt ? new Date(post.publishedAt).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }) : new Date().toLocaleDateString(),
      author: post.authorId?.name || 'Admin',
      category: post.categories?.[0] || 'General',
      image: post.featuredImage || '/blog-placeholder.jpg',
      description: post.description || '',
      imageCredit: post.imageCredit || '',
      tags: post.tags || [],
    };
  } catch (error) {
    console.error("Error fetching blog post:", error);
    // Fallback to static data
    const staticPost = blogPosts[slug as keyof typeof blogPosts];
    return staticPost || null;
  }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params;
  await connectToDatabase();
  const post = await BlogPost.findOne({ slug });
  if (!post) {
    return {
      title: "Post Not Found - ToolCrush Blog",
      description: "Blog post not found",
    };
  }
  return {
    title: post.title,
    description: post.excerpt || "Read this amazing blog post on ToolCrush",
  };
}

export default async function BlogPostPage({ params }: Props) {
  const { slug } = await params;
  const post = await getBlogPost(slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground">
      <Header />
      <BlogPostContent post={post} slug={slug} />
      <Footer />
    </div>
  );
}
